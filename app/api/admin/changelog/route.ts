import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { changelogTypeReqSchema, ChangelogWithPartialTranslations } from "@/@types/admin/changelog/changelog";
import { changelogSchema } from "@/server/db/schema.server";
import { desc } from "drizzle-orm";

export async function GET(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const params: any = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params:", params);
	}
	try {
		changelogTypeReqSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	const db = getDB();
	const changelogs: ChangelogWithPartialTranslations[] = await db.query.changelogSchema.findMany({
		with: {
			translations: {
				columns: {
					id: true,
					changelogId: true,
					lang: true,
					status: true,
					title: true,
					publishedAt: true,
					createdAt: true,
					updatedAt: true,
				},
			},
		},
		orderBy: [desc(changelogSchema.createdAt)],
		// limit: pageSize,
		// offset: offset,
	});

	return NextResponse.json({ status: 200, changelogs });
}
