import { getKVKeyChangelogHeads } from "@/lib/utils";
import { DURATION_1_WEEK } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { changelogTranslationSchema } from "./db/schema.server";
import { and, eq, desc } from "drizzle-orm";
import { getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { ChangelogHead, changelogHeadSchema, ChangelogStatus } from "@/@types/admin/changelog/changelog";

export async function getChangelogs(lang: string = "en", page: number = 1): Promise<{ changelogs: ChangelogHead[] }> {
	const pageSize = 20;
	const offset = (page - 1) * pageSize;

	//  Get changelogs from kv
	let changelogs: ChangelogHead[] = [];
	//先从kv中获取
	const cacheKeyChangelogs = getKVKeyChangelogHeads(lang, page);
	const kvDataChangelogs = (await getValue(cacheKeyChangelogs)) as any;
	if (kvDataChangelogs) {
		try {
			const changelogsParse = superjson.deserialize(kvDataChangelogs) as ChangelogHead[];
			// console.log("blogHeadsParse:", blogHeadsParse);
			changelogHeadSchema.array().parse(changelogsParse);
			changelogs = changelogsParse;
		} catch (error) {
			console.log("[getChangelogs] parse changelogs data from kv error:", error);
		}
	}
	//再从db中获取

	if (changelogs.length === 0) {
		const db = getDB();
		const changelogWithTranslations = await db.query.changelogTranslationSchema.findMany({
			columns: {
				id: true,
				changelogId: true,
				lang: true,
				status: true,
				image: true,
				title: true,
				html: true,
				publishedAt: true,
			},
			where: and(eq(changelogTranslationSchema.lang, lang), eq(changelogTranslationSchema.status, ChangelogStatus.Published)),
			with: {
				changelog: {
					columns: {
						majorVersion: true,
						minorVersion: true,
						patchVersion: true,
						image: true,
					},
				},
			},
			orderBy: [desc(changelogTranslationSchema.publishedAt)],
			limit: pageSize,
			offset: offset,
		});
		if (changelogWithTranslations.length > 0) {
			changelogs = changelogWithTranslations.map((changelogWithTranslation) => ({
				id: changelogWithTranslation.id,
				lang: changelogWithTranslation.lang,
				majorVersion: changelogWithTranslation.changelog.majorVersion,
				minorVersion: changelogWithTranslation.changelog.minorVersion,
				patchVersion: changelogWithTranslation.changelog.patchVersion,
				title: changelogWithTranslation.title,
				image: changelogWithTranslation.image ?? changelogWithTranslation.changelog.image,
				status: changelogWithTranslation.status,
				html: changelogWithTranslation.html,
				publishedAt: changelogWithTranslation.publishedAt,
			}));
			await setValue(cacheKeyChangelogs, superjson.stringify(changelogs), DURATION_1_WEEK);
		}
	}

	return { changelogs };
}
